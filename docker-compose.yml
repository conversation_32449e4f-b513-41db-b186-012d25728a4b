version: '3.8'

services:
  api:
    build: .
    ports:
      - "8001:8000"
    volumes:
      - .:/app
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=false
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
        # Can be v1, v2, or both
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - WHISPER_MODEL=base
      - PREMIUM_VIDEO_EFFECTS=["blur", "grayscale", "sepia"]
      - PREMIUM_AUDIO_EFFECTS=["echo", "reverb"]
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  celery_worker:
    build: .
    command: celery -A app.utils.job_queue.celery_app worker --loglevel=info
    volumes:
      - .:/app
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=false
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
      
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - WHISPER_MODEL=base
      - PREMIUM_VIDEO_EFFECTS=["blur", "grayscale", "sepia"]
      - PREMIUM_AUDIO_EFFECTS=["echo", "reverb"]
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
