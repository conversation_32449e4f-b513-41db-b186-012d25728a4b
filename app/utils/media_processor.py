"""
Media processor implementation for handling media operations.
"""
import os
import logging
from enum import Enum
from typing import Optional, Dict, Any, List
import ffmpeg
import cv2
import numpy as np
from pydantic import BaseModel
from app.models.job import JobProgress
from app.utils.storage import storage_provider

logger = logging.getLogger(__name__)

class MediaFormat(str, Enum):
    """Supported media formats."""
    MP4 = "mp4"
    MP3 = "mp3"
    WAV = "wav"
    AAC = "aac"
    OGG = "ogg"
    WEBM = "webm"

class MediaProcessor:
    def __init__(self):
        """Initialize media processor."""
        self.temp_dir = storage_provider.temp_dir

    async def transcode_media(
        self,
        input_path: str,
        output_format: MediaFormat,
        progress_callback: Optional[callable] = None
    ) -> str:
        """
        Transcode media to a different format.
        
        Args:
            input_path: Path to input file
            output_format: Desired output format
            progress_callback: Optional callback for progress updates
            
        Returns:
            Path to transcoded file
        """
        try:
            # Generate output path
            output_path = os.path.join(
                self.temp_dir,
                f"transcoded_{os.path.basename(input_path)}.{output_format}"
            )
            
            # Get input duration for progress tracking
            probe = ffmpeg.probe(input_path)
            duration = float(probe['format']['duration'])
            
            # Setup ffmpeg command
            stream = ffmpeg.input(input_path)
            
            if output_format in [MediaFormat.MP3, MediaFormat.WAV, MediaFormat.AAC, MediaFormat.OGG]:
                # Audio-only output
                stream = ffmpeg.output(
                    stream,
                    output_path,
                    acodec='libmp3lame' if output_format == MediaFormat.MP3 else None,
                    **{'progress': '-' if progress_callback else None}
                )
            else:
                # Video output
                stream = ffmpeg.output(
                    stream,
                    output_path,
                    vcodec='libx264' if output_format == MediaFormat.MP4 else None,
                    acodec='aac',
                    **{'progress': '-' if progress_callback else None}
                )
            
            # Run transcoding
            process = ffmpeg.run_async(
                stream,
                pipe_stdout=True,
                pipe_stderr=True
            )
            
            # Track progress if callback provided
            if progress_callback:
                while True:
                    line = process.stderr.readline().decode('utf-8')
                    if not line:
                        break
                        
                    # Parse progress info
                    if "time=" in line:
                        time_str = line.split("time=")[1].split()[0]
                        current_time = self._parse_ffmpeg_time(time_str)
                        
                        if current_time and duration:
                            progress = JobProgress(
                                current=int(current_time),
                                total=int(duration),
                                percentage=(current_time / duration * 100),
                                message="Transcoding media"
                            )
                            await progress_callback(progress)
            
            process.wait()
            
            if process.returncode != 0:
                raise Exception("Transcoding failed")
                
            return output_path
        except Exception as e:
            logger.error(f"Error transcoding media: {str(e)}")
            raise ValueError(f"Failed to transcode media: {str(e)}")

    async def extract_audio(
        self,
        input_path: str,
        output_format: MediaFormat = MediaFormat.MP3,
        progress_callback: Optional[callable] = None
    ) -> str:
        """
        Extract audio from media file.
        
        Args:
            input_path: Path to input file
            output_format: Desired audio format
            progress_callback: Optional callback for progress updates
            
        Returns:
            Path to extracted audio file
        """
        try:
            # Generate output path
            output_path = os.path.join(
                self.temp_dir,
                f"audio_{os.path.basename(input_path)}.{output_format}"
            )
            
            # Get input duration for progress tracking
            probe = ffmpeg.probe(input_path)
            duration = float(probe['format']['duration'])
            
            # Setup ffmpeg command for audio extraction
            stream = ffmpeg.input(input_path)
            stream = ffmpeg.output(
                stream,
                output_path,
                acodec='libmp3lame' if output_format == MediaFormat.MP3 else None,
                vn=None,  # No video
                **{'progress': '-' if progress_callback else None}
            )
            
            # Run extraction
            process = ffmpeg.run_async(
                stream,
                pipe_stdout=True,
                pipe_stderr=True
            )
            
            # Track progress if callback provided
            if progress_callback:
                while True:
                    line = process.stderr.readline().decode('utf-8')
                    if not line:
                        break
                        
                    # Parse progress info
                    if "time=" in line:
                        time_str = line.split("time=")[1].split()[0]
                        current_time = self._parse_ffmpeg_time(time_str)
                        
                        if current_time and duration:
                            progress = JobProgress(
                                current=int(current_time),
                                total=int(duration),
                                percentage=(current_time / duration * 100),
                                message="Extracting audio"
                            )
                            await progress_callback(progress)
            
            process.wait()
            
            if process.returncode != 0:
                raise Exception("Audio extraction failed")
                
            return output_path
        except Exception as e:
            logger.error(f"Error extracting audio: {str(e)}")
            raise ValueError(f"Failed to extract audio: {str(e)}")

    def _parse_ffmpeg_time(self, time_str: str) -> Optional[float]:
        """Parse FFmpeg time string into seconds."""
        try:
            # Handle HH:MM:SS.ms format
            if ':' in time_str:
                h, m, s = time_str.split(':')
                return float(h) * 3600 + float(m) * 60 + float(s)
            return float(time_str)
        except:
            return None

    def transcode_media_sync(
        self,
        input_path: str,
        output_format: MediaFormat,
        progress_callback: Optional[callable] = None
    ) -> str:
        """Synchronous version of transcode_media."""
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.transcode_media(input_path, output_format, progress_callback)
            )
        finally:
            loop.close()

    def extract_audio_sync(
        self,
        input_path: str,
        output_format: MediaFormat = MediaFormat.MP3,
        progress_callback: Optional[callable] = None
    ) -> str:
        """Synchronous version of extract_audio."""
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.extract_audio(input_path, output_format, progress_callback)
            )
        finally:
            loop.close()

# Create global media processor instance
media_processor = MediaProcessor()
