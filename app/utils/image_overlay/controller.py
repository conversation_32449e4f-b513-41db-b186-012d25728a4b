"""
Image overlay controller implementation.
"""
import os
import logging
import async<PERSON>
from typing import Optional, Dict, Any
from PIL import Image, ImageDraw, ImageFont
from app.models.job import JobProgress
from app.utils.storage import storage_provider

logger = logging.getLogger(__name__)

class ImageOverlayController:
    def __init__(self):
        """Initialize image overlay controller."""
        self.temp_dir = storage_provider.temp_dir
        self.fonts_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'fonts')

    async def process_overlay(
        self,
        input_data: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Process image overlay.
        
        Args:
            input_data: Input data containing image paths and settings
            progress_callback: Optional callback for progress updates
            
        Returns:
            Output data with processed image path
        """
        try:
            # Extract input parameters
            base_image_path = input_data['base_image']
            overlay_image_path = input_data.get('overlay_image')
            text = input_data.get('text')
            text_settings = input_data.get('text_settings', {})
            
            # Open base image
            base_image = Image.open(base_image_path)
            
            # Track progress
            total_steps = 1 + bool(overlay_image_path) + bool(text)
            current_step = 0
            
            if progress_callback:
                await progress_callback(JobProgress(
                    current=current_step,
                    total=total_steps,
                    percentage=0,
                    message="Processing base image"
                ))
            
            # Add overlay image if provided
            if overlay_image_path:
                current_step += 1
                if progress_callback:
                    await progress_callback(JobProgress(
                        current=current_step,
                        total=total_steps,
                        percentage=(current_step / total_steps * 100),
                        message="Adding overlay image"
                    ))
                
                overlay = Image.open(overlay_image_path)
                position = input_data.get('overlay_position', (0, 0))
                base_image.paste(overlay, position, overlay if overlay.mode == 'RGBA' else None)
            
            # Add text if provided
            if text:
                current_step += 1
                if progress_callback:
                    await progress_callback(JobProgress(
                        current=current_step,
                        total=total_steps,
                        percentage=(current_step / total_steps * 100),
                        message="Adding text overlay"
                    ))
                
                draw = ImageDraw.Draw(base_image)
                
                # Load font
                font_name = text_settings.get('font', 'Arial.ttf')
                font_size = text_settings.get('size', 32)
                font_path = os.path.join(self.fonts_dir, font_name)
                font = ImageFont.truetype(font_path, font_size)
                
                # Get text position
                position = text_settings.get('position', (0, 0))
                
                # Get text color (default to black)
                color = text_settings.get('color', (0, 0, 0, 255))
                
                # Draw text
                draw.text(position, text, font=font, fill=color)
            
            # Save result
            output_path = os.path.join(
                self.temp_dir,
                f"overlay_{os.path.basename(base_image_path)}"
            )
            base_image.save(output_path)
            
            return {
                'output_path': output_path,
                'message': 'Image overlay processed successfully'
            }
        except Exception as e:
            logger.error(f"Error processing image overlay: {str(e)}")
            raise ValueError(f"Failed to process image overlay: {str(e)}")

    def process_overlay_sync(
        self,
        input_data: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """Synchronous version of process_overlay."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.process_overlay(input_data, progress_callback)
            )
        finally:
            loop.close()

# Create global controller instance
image_overlay_controller = ImageOverlayController()
