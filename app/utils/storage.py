"""
Storage provider implementation for handling file storage.
"""
import os
import shutil
from typing import Optional, Dict, BinaryIO, Union, Any
from pathlib import Path
from datetime import datetime
import boto3
from botocore.exceptions import ClientError
from fastapi import HTTPException, UploadFile
import logging


logger = logging.getLogger(__name__)

class StorageProvider:
    def __init__(self):
        """Initialize storage provider based on configuration."""
        self.storage_type = os.getenv("STORAGE_TYPE", "local")
        self.temp_dir = os.getenv("TEMP_DIR", "/tmp/media_processing")
        self.initialized = False
        
        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)

    async def initialize(self):
        """Initialize storage provider connections."""
        if self.initialized:
            return
        
        if self.storage_type == "s3":
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            self.bucket_name = os.getenv("S3_BUCKET")
            if not self.bucket_name:
                raise ValueError("S3_BUCKET environment variable is required when storage_type is 's3'")
        
        self.initialized = True

    async def cleanup(self):
        """Cleanup temporary files and connections."""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            return True
        except Exception as e:
            logger.error(f"Error cleaning up storage: {str(e)}")
            return False

    async def get_status(self) -> Dict[str, Any]:
        """Get storage provider status."""
        status = {
            "type": self.storage_type,
            "temp_dir": self.temp_dir,
            "initialized": self.initialized,
            "temp_dir_exists": os.path.exists(self.temp_dir)
        }
        
        if self.storage_type == "s3":
            try:
                self.s3_client.head_bucket(Bucket=self.bucket_name)
                status["s3_connected"] = True
                status["bucket"] = self.bucket_name
            except Exception:
                status["s3_connected"] = False
        
        return status

    async def save_upload(self, upload_file: Union[BinaryIO, UploadFile], filename: str) -> str:
        """
        Save an uploaded file.
        
        Args:
            upload_file: The uploaded file object
            filename: Original filename
            
        Returns:
            Path where file was saved
        """
        # Generate a unique filename to avoid collisions
        unique_filename = f"{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{filename}"
        return await self.save_file(upload_file, unique_filename)

    async def save_file(self, file: BinaryIO, path: str, metadata: Optional[Dict[str, str]] = None) -> str:
        """
        Save a file to storage.
        
        Args:
            file: File-like object to save
            path: Path where to save the file
            metadata: Optional metadata to store with the file
            
        Returns:
            URL or path where the file was saved
        """
        if self.storage_type == "local":
            return await self._save_local(file, path)
        elif self.storage_type == "s3":
            return await self._save_s3(file, path, metadata)
        else:
            raise ValueError(f"Unsupported storage type: {self.storage_type}")

    async def get_file(self, path: str) -> BinaryIO:
        """
        Get a file from storage.
        
        Args:
            path: Path of the file to get
            
        Returns:
            File-like object
        """
        if self.storage_type == "local":
            return await self._get_local(path)
        elif self.storage_type == "s3":
            return await self._get_s3(path)
        else:
            raise ValueError(f"Unsupported storage type: {self.storage_type}")

    async def delete_file(self, path: str) -> bool:
        """
        Delete a file from storage.
        
        Args:
            path: Path of the file to delete
            
        Returns:
            True if file was deleted, False otherwise
        """
        if self.storage_type == "local":
            return await self._delete_local(path)
        elif self.storage_type == "s3":
            return await self._delete_s3(path)
        else:
            raise ValueError(f"Unsupported storage type: {self.storage_type}")

    async def _save_local(self, file: BinaryIO, path: str) -> str:
        """Save file to local storage."""
        try:
            full_path = os.path.join(self.temp_dir, path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            with open(full_path, 'wb') as f:
                shutil.copyfileobj(file, f)
            
            return full_path
        except Exception as e:
            logger.error(f"Error saving file locally: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to save file")

    async def _get_local(self, path: str) -> BinaryIO:
        """Get file from local storage."""
        try:
            full_path = os.path.join(self.temp_dir, path)
            if not os.path.exists(full_path):
                raise HTTPException(status_code=404, detail="File not found")
            
            return open(full_path, 'rb')
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting file locally: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to get file")

    async def _delete_local(self, path: str) -> bool:
        """Delete file from local storage."""
        try:
            full_path = os.path.join(self.temp_dir, path)
            if os.path.exists(full_path):
                os.remove(full_path)
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting file locally: {str(e)}")
            return False

    async def _save_s3(self, file: BinaryIO, path: str, metadata: Optional[Dict[str, str]] = None) -> str:
        """Save file to S3."""
        try:
            extra_args = {}
            if metadata:
                extra_args['Metadata'] = metadata
            
            self.s3_client.upload_fileobj(
                file,
                self.bucket_name,
                path,
                ExtraArgs=extra_args
            )
            
            return f"s3://{self.bucket_name}/{path}"
        except ClientError as e:
            logger.error(f"Error saving file to S3: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to save file to S3")

    async def _get_s3(self, path: str) -> BinaryIO:
        """Get file from S3."""
        try:
            # Create a temporary file to store the S3 object
            temp_path = os.path.join(self.temp_dir, os.path.basename(path))
            with open(temp_path, 'wb') as f:
                self.s3_client.download_fileobj(self.bucket_name, path, f)
            
            return open(temp_path, 'rb')
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise HTTPException(status_code=404, detail="File not found in S3")
            logger.error(f"Error getting file from S3: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to get file from S3")

    async def _delete_s3(self, path: str) -> bool:
        """Delete file from S3."""
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=path
            )
            return True
        except ClientError as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            return False

    async def get_file_metadata(self, path: str) -> Optional[Dict[str, str]]:
        """
        Get metadata for a file.
        
        Args:
            path: Path of the file
            
        Returns:
            Dictionary of metadata or None if not found
        """
        try:
            if self.storage_type == "s3":
                response = self.s3_client.head_object(
                    Bucket=self.bucket_name,
                    Key=path
                )
                return response.get('Metadata')
            else:
                # For local storage, we don't maintain metadata
                return None
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            logger.error(f"Error getting file metadata: {str(e)}")
            return None

# Create global storage provider instance
storage_provider = StorageProvider()
