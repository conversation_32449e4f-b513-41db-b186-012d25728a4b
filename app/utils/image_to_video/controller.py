"""
Image to video controller implementation.
"""
import os
import logging
import asyncio
from typing import Optional, Dict, Any, List
import cv2
import numpy as np
import ffmpeg
from app.models.job import JobProgress
from app.utils.storage import storage_provider

logger = logging.getLogger(__name__)

class ImageToVideoController:
    def __init__(self):
        """Initialize image to video controller."""
        self.temp_dir = storage_provider.temp_dir

    async def create_video(
        self,
        input_data: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Create video from image.
        
        Args:
            input_data: Input data containing image path and settings
            progress_callback: Optional callback for progress updates
            
        Returns:
            Output data with video path
        """
        try:
            # Extract input parameters
            image_path = input_data['image_path']
            duration = input_data.get('duration', 10)  # Default 10 seconds
            fps = input_data.get('fps', 30)  # Default 30fps
            resolution = input_data.get('resolution', (1920, 1080))
            audio_path = input_data.get('audio_path')
            
            # Load and resize image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            image = cv2.resize(image, resolution)
            
            # Generate output path
            output_path = os.path.join(
                self.temp_dir,
                f"video_{os.path.basename(image_path)}.mp4"
            )
            
            # Calculate total frames
            total_frames = duration * fps
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, resolution)
            
            # Write frames
            for frame in range(total_frames):
                if progress_callback:
                    await progress_callback(JobProgress(
                        current=frame,
                        total=total_frames,
                        percentage=(frame / total_frames * 100),
                        message="Generating video frames"
                    ))
                out.write(image)
            
            out.release()
            
            # Add audio if provided
            if audio_path:
                if progress_callback:
                    await progress_callback(JobProgress(
                        current=total_frames,
                        total=total_frames + 1,
                        percentage=95,
                        message="Adding audio track"
                    ))
                
                # Generate temporary output path for final video
                final_output = os.path.join(
                    self.temp_dir,
                    f"final_{os.path.basename(output_path)}"
                )
                
                # Add audio using ffmpeg
                stream = ffmpeg.input(output_path)
                audio = ffmpeg.input(audio_path)
                stream = ffmpeg.output(
                    stream,
                    audio,
                    final_output,
                    acodec='aac',
                    vcodec='copy'
                )
                ffmpeg.run(stream, overwrite_output=True)
                
                # Replace original output with final version
                os.replace(final_output, output_path)
            
            if progress_callback:
                await progress_callback(JobProgress(
                    current=total_frames + 1,
                    total=total_frames + 1,
                    percentage=100,
                    message="Video generation complete"
                ))
            
            return {
                'output_path': output_path,
                'duration': duration,
                'fps': fps,
                'resolution': resolution,
                'message': 'Video created successfully'
            }
        except Exception as e:
            logger.error(f"Error creating video: {str(e)}")
            raise ValueError(f"Failed to create video: {str(e)}")

    def create_video_sync(
        self,
        input_data: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """Synchronous version of create_video."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.create_video(input_data, progress_callback)
            )
        finally:
            loop.close()

# Create global controller instance
image_to_video_controller = ImageToVideoController()
