"""
Media downloader implementation for handling remote media downloads.
"""
import os
import re
import logging
import asyncio
from typing import Optional, Dict, Any, List
import aiohttp
import yt_dlp
from pydantic import BaseModel, HttpUrl
from app.models.job import JobStatus, JobProgress
from app.utils.storage import storage_provider

logger = logging.getLogger(__name__)

class DownloadFormat(BaseModel):
    format_id: str
    ext: str
    resolution: Optional[str] = None
    filesize: Optional[int] = None
    tbr: Optional[float] = None  # Total bitrate
    protocol: str
    url: str

class MediaInfo(BaseModel):
    title: str
    description: Optional[str] = None
    duration: Optional[float] = None
    formats: List[DownloadFormat]
    thumbnail: Optional[str] = None
    webpage_url: Optional[str] = None

class MediaDownloader:
    def __init__(self):
        """Initialize media downloader."""
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }

    async def get_media_info(self, url: str) -> MediaInfo:
        """
        Get information about a media URL.
        
        Args:
            url: URL to get information for
            
        Returns:
            Media information
        """
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                formats = []
                for f in info.get('formats', []):
                    formats.append(DownloadFormat(
                        format_id=f['format_id'],
                        ext=f['ext'],
                        resolution=f.get('resolution'),
                        filesize=f.get('filesize'),
                        tbr=f.get('tbr'),
                        protocol=f.get('protocol', 'https'),
                        url=f['url']
                    ))
                
                return MediaInfo(
                    title=info.get('title', ''),
                    description=info.get('description'),
                    duration=info.get('duration'),
                    formats=formats,
                    thumbnail=info.get('thumbnail'),
                    webpage_url=info.get('webpage_url')
                )
        except Exception as e:
            logger.error(f"Error getting media info: {str(e)}")
            raise ValueError(f"Failed to get media info: {str(e)}")

    def get_media_info_sync(self, url: str) -> MediaInfo:
        """Synchronous version of get_media_info."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.get_media_info(url))
        finally:
            loop.close()

    async def download_media(
        self,
        url: str,
        format_id: Optional[str] = None,
        progress_callback: Optional[callable] = None
    ) -> str:
        """
        Download media from URL.
        
        Args:
            url: URL to download from
            format_id: Optional format ID to download
            progress_callback: Optional callback for progress updates
            
        Returns:
            Path to downloaded file
        """
        try:
            # Check if it's a direct media URL
            if self._is_direct_media_url(url):
                return await self._download_direct(url, progress_callback)
            
            # Otherwise use yt-dlp
            return await self._download_with_ytdl(url, format_id, progress_callback)
        except Exception as e:
            logger.error(f"Error downloading media: {str(e)}")
            raise ValueError(f"Failed to download media: {str(e)}")

    def download_media_sync(
        self,
        url: str,
        format_id: Optional[str] = None,
        progress_callback: Optional[callable] = None
    ) -> str:
        """Synchronous version of download_media."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.download_media(url, format_id, progress_callback)
            )
        finally:
            loop.close()

    def _is_direct_media_url(self, url: str) -> bool:
        """Check if URL points directly to a media file."""
        media_extensions = r'\.(mp4|mkv|mov|avi|mp3|wav|aac|m4a|webm|ogg)$'
        return bool(re.search(media_extensions, url.lower()))

    async def _download_direct(self, url: str, progress_callback: Optional[callable] = None) -> str:
        """Download from direct media URL."""
        try:
            # Generate temporary filename
            filename = os.path.basename(url)
            if not filename:
                filename = f"download_{os.urandom(4).hex()}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response.raise_for_status()
                    total_size = int(response.headers.get('content-length', 0))
                    
                    # Create temporary file
                    temp_path = os.path.join(storage_provider.temp_dir, filename)
                    
                    chunk_size = 8192
                    downloaded = 0
                    
                    with open(temp_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(chunk_size):
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if progress_callback and total_size:
                                progress = JobProgress(
                                    current=downloaded,
                                    total=total_size,
                                    percentage=(downloaded / total_size * 100),
                                    message="Downloading media file"
                                )
                                await progress_callback(progress)
            
            return temp_path
        except Exception as e:
            logger.error(f"Error downloading from direct URL: {str(e)}")
            raise ValueError(f"Failed to download from URL: {str(e)}")

    async def _download_with_ytdl(
        self,
        url: str,
        format_id: Optional[str] = None,
        progress_callback: Optional[callable] = None
    ) -> str:
        """Download using yt-dlp."""
        try:
            # Create temporary directory for download
            temp_dir = os.path.join(storage_provider.temp_dir, 'ytdl')
            os.makedirs(temp_dir, exist_ok=True)
            
            ydl_opts = {
                **self.ydl_opts,
                'format': format_id if format_id else 'best',
                'outtmpl': os.path.join(temp_dir, '%(title)s.%(ext)s'),
            }
            
            if progress_callback:
                ydl_opts['progress_hooks'] = [
                    lambda d: self._progress_hook(d, progress_callback)
                ]
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                filename = ydl.prepare_filename(info)
                return filename
        except Exception as e:
            logger.error(f"Error downloading with yt-dlp: {str(e)}")
            raise ValueError(f"Failed to download media: {str(e)}")

    async def _progress_hook(self, d: Dict[str, Any], callback: callable):
        """Progress hook for yt-dlp."""
        if d['status'] == 'downloading':
            try:
                downloaded = d.get('downloaded_bytes', 0)
                total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
                
                if total > 0:
                    progress = JobProgress(
                        current=downloaded,
                        total=total,
                        percentage=(downloaded / total * 100),
                        message=f"Downloading {d.get('filename', '')}"
                    )
                    await callback(progress)
            except Exception as e:
                logger.error(f"Error in progress hook: {str(e)}")

# Create global media downloader instance
media_downloader = MediaDownloader()
