"""
Authentication utilities for JWT-based authentication with premium user support.
"""
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Union
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None
    is_premium: bool = False
    exp: Optional[datetime] = None

class User(BaseModel):
    username: str
    is_premium: bool = False
    disabled: Optional[bool] = None

def create_access_token(data: Dict[str, Union[str, bool]], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time
        
    Returns:
        Encoded JWT token
    """
    to_encode = {
        **data,
        "exp": int((datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))).timestamp())
    }
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    """
    Validate and decode JWT token to get current user.
    
    Args:
        token: JWT token from request
        
    Returns:
        User object
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if not isinstance(username, str):
            raise credentials_exception
        is_premium = bool(payload.get("is_premium", False))
        token_data = TokenData(username=username, is_premium=is_premium)
    except JWTError:
        raise credentials_exception
        
    user = User(username=token_data.username, is_premium=token_data.is_premium)
    if user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user

def require_premium():
    """
    Decorator to require premium access for a route.
    
    Usage:
        @app.get("/premium-feature")
        @require_premium()
        async def premium_feature(current_user: User = Depends(get_current_user)):
            return {"message": "This is a premium feature"}
    """
    def decorator(func):
        async def wrapper(*args, current_user: User = Depends(get_current_user), **kwargs):
            if not current_user.is_premium:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Premium subscription required for this feature"
                )
            return await func(*args, current_user=current_user, **kwargs)
        return wrapper
    return decorator

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)
