"""
Job queue implementation for handling background tasks.
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from celery import Celery
from celery.result import AsyncResult
from app.models.job import Job, JobType, JobStatus, JobProgress

logger = logging.getLogger(__name__)

# Initialize Celery
celery_app = Celery(
    'media_processing',
    broker=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

class JobQueue:
    def __init__(self):
        """Initialize job queue."""
        self.jobs: Dict[str, Job] = {}

    async def create_job(
        self,
        job_type: JobType,
        input_data: Dict[str, Any],
        user_id: str,
        is_premium: bool = False
    ) -> Job:
        """
        Create a new job.
        
        Args:
            job_type: Type of job to create
            input_data: Input data for the job
            user_id: ID of user creating the job
            is_premium: Whether user has premium access
            
        Returns:
            Created job
        """
        job_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        job = Job(
            id=job_id,
            type=job_type,
            status=JobStatus.PENDING,
            user_id=user_id,
            is_premium=is_premium,
            input_data=input_data,
            created_at=now,
            updated_at=now
        )
        
        self.jobs[job_id] = job
        
        # Start Celery task based on job type
        if job_type == JobType.MEDIA_DOWNLOAD:
            celery_app.send_task(
                'tasks.download_media',
                args=[job_id, input_data],
                task_id=job_id
            )
        elif job_type == JobType.MEDIA_TRANSCRIPTION:
            celery_app.send_task(
                'tasks.transcribe_media',
                args=[job_id, input_data],
                task_id=job_id
            )
        elif job_type == JobType.IMAGE_OVERLAY:
            celery_app.send_task(
                'tasks.process_image_overlay',
                args=[job_id, input_data],
                task_id=job_id
            )
        elif job_type == JobType.IMAGE_TO_VIDEO:
            celery_app.send_task(
                'tasks.convert_image_to_video',
                args=[job_id, input_data],
                task_id=job_id
            )
        
        return job

    async def get_job_status(self, job_id: str) -> Optional[Job]:
        """
        Get status of a job.
        
        Args:
            job_id: ID of job to get status for
            
        Returns:
            Job if found, None otherwise
        """
        job = self.jobs.get(job_id)
        if not job:
            return None
            
        # Get Celery task status
        result = AsyncResult(job_id, app=celery_app)
        
        if result.ready():
            if result.successful():
                job.status = JobStatus.COMPLETED
                job.output_data = result.get()
            else:
                job.status = JobStatus.FAILED
                job.error = str(result.get(propagate=False))
            job.completed_at = datetime.utcnow()
        elif result.state == 'PROGRESS':
            job.status = JobStatus.PROCESSING
            meta = result.info
            if isinstance(meta, dict):
                job.progress = JobProgress(
                    current=meta.get('current', 0),
                    total=meta.get('total', 100),
                    percentage=meta.get('percentage', 0.0),
                    message=meta.get('message')
                )
        
        job.updated_at = datetime.utcnow()
        return job

    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a job.
        
        Args:
            job_id: ID of job to cancel
            
        Returns:
            True if job was cancelled, False otherwise
        """
        job = self.jobs.get(job_id)
        if not job:
            return False
            
        # Revoke Celery task
        celery_app.control.revoke(job_id, terminate=True)
        
        job.status = JobStatus.CANCELLED
        job.updated_at = datetime.utcnow()
        job.completed_at = datetime.utcnow()
        return True

    async def cleanup_old_jobs(self, max_age_hours: int = 24):
        """
        Clean up old completed jobs.
        
        Args:
            max_age_hours: Maximum age of jobs to keep in hours
        """
        now = datetime.utcnow()
        to_remove = []
        
        for job_id, job in self.jobs.items():
            if job.completed_at:
                age = now - job.completed_at
                if age.total_seconds() > max_age_hours * 3600:
                    to_remove.append(job_id)
        
        for job_id in to_remove:
            del self.jobs[job_id]

# Create global job queue instance
job_queue = JobQueue()
