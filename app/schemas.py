from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

class MediaFormat(str, Enum):
    """Media format enum."""
    # Video formats
    MP4 = "mp4"
    MOV = "mov"
    AVI = "avi"
    MKV = "mkv"
    
    # Audio formats
    MP3 = "mp3"
    WAV = "wav"
    AAC = "aac"
    FLAC = "flac"
    
    # Image formats
    JPG = "jpg"
    PNG = "png"
    GIF = "gif"
    WEBP = "webp"

class VideoEffect(str, Enum):
    """Video effect enum."""
    BLUR = "blur"
    GRAYSCALE = "grayscale"
    SEPIA = "sepia"

class AudioEffect(str, Enum):
    """Audio effect enum."""
    ECHO = "echo"
    REVERB = "reverb"

class StatusEnum(str, Enum):
    """Processing status enum."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class StorageInfo(BaseModel):
    """Storage information."""
    url: str
    bucket: Optional[str] = None
    key: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ProcessingResult(BaseModel):
    """Processing result."""
    status: StatusEnum
    output_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None



class Caption(BaseModel):
    """Video caption."""
    text: str
    start_time: float
    end_time: float
    position: Optional[str] = "bottom"  # top, middle, bottom
    font_size: Optional[int] = 32
    font_color: Optional[str] = "#ffffff"  # hex color
    background_color: Optional[str] = "#000000"  # hex color
    background_opacity: Optional[float] = 0.5

class TokenData(BaseModel):
    """JWT token data."""
    username: str
    is_premium: bool = False
    exp: Optional[int] = None

class User(BaseModel):
    """User model."""
    username: str
    email: str
    is_premium: bool = False
    disabled: bool = False

class UserInDB(User):
    """User model with hashed password."""
    hashed_password: str

class Token(BaseModel):
    """Token model."""
    access_token: str
    token_type: str = "bearer"

class StorageStatus(BaseModel):
    """Storage provider status."""
    status: str
    provider: str
    details: Optional[Dict[str, Any]] = None

class JobStatus(BaseModel):
    """Background job status."""
    job_id: str
    status: StatusEnum
    progress: Optional[float] = None
    result: Optional[ProcessingResult] = None
    error: Optional[str] = None

class WebhookPayload(BaseModel):
    """Webhook payload."""
    event: str
    job_id: str
    status: StatusEnum
    result: Optional[ProcessingResult] = None
    error: Optional[str] = None
    timestamp: float
