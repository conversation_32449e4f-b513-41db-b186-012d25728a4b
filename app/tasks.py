"""
Celery tasks for handling background jobs.
"""
import os
import logging
from celery import Celery, Task
from celery.signals import celeryd_after_setup
from app.utils.media_processor import media_processor, MediaFormat
from app.utils.media_downloader import media_downloader
from app.utils.storage import storage_provider
from app.utils.image_overlay.controller import ImageOverlayController
from app.utils.image_to_video.controller import ImageToVideoController

logger = logging.getLogger(__name__)

# Initialize Celery
celery_app = Celery(
    'media_processing',
    broker=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

class BaseTask(Task):
    """Base task with common functionality."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        logger.error(f"Task {task_id} failed: {str(exc)}")
        super().on_failure(exc, task_id, args, kwargs, einfo)

@celery_app.task(base=BaseTask, bind=True)
def download_media(self, job_id: str, input_data: dict) -> dict:
    """
    Download media from URL.
    
    Args:
        job_id: ID of the job
        input_data: Input data containing URL and format
        
    Returns:
        Output data with downloaded file path
    """
    try:
        url = input_data['url']
        format_id = input_data.get('format_id')
        
        # Download media with progress tracking
        def progress_callback(progress):
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress.current,
                    'total': progress.total,
                    'percentage': progress.percentage,
                    'message': progress.message
                }
            )
        
        # Use synchronous version for Celery task
        file_path = media_downloader.download_media_sync(
            url,
            format_id=format_id,
            progress_callback=progress_callback
        )
        
        return {
            'file_path': file_path,
            'message': 'Media downloaded successfully'
        }
    except Exception as e:
        logger.error(f"Error in download task: {str(e)}")
        raise

@celery_app.task(base=BaseTask, bind=True)
def transcribe_media(self, job_id: str, input_data: dict) -> dict:
    """
    Transcribe media file.
    
    Args:
        job_id: ID of the job
        input_data: Input data containing file path and settings
        
    Returns:
        Output data with transcription
    """
    try:
        file_path = input_data['file_path']
        language = input_data.get('language', 'en')
        model = input_data.get('model', 'base')
        timestamps = input_data.get('timestamps', False)
        diarization = input_data.get('diarization', False)
        
        # Extract audio if needed
        if not file_path.endswith(('.mp3', '.wav')):
            def progress_callback(progress):
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': progress.current,
                        'total': progress.total,
                        'percentage': progress.percentage,
                        'message': 'Extracting audio'
                    }
                )
            
            # Use synchronous version for Celery task
            file_path = media_processor.extract_audio_sync(
                file_path,
                output_format=MediaFormat.WAV,
                progress_callback=progress_callback
            )
        
        # TODO: Implement actual transcription
        # For now, return dummy response
        return {
            'text': 'Transcription not implemented yet',
            'language': language,
            'timestamps': [] if timestamps else None,
            'speakers': [] if diarization else None
        }
    except Exception as e:
        logger.error(f"Error in transcription task: {str(e)}")
        raise

@celery_app.task(base=BaseTask, bind=True)
def process_image_overlay(self, job_id: str, input_data: dict) -> dict:
    """
    Process image overlay.
    
    Args:
        job_id: ID of the job
        input_data: Input data containing image paths and settings
        
    Returns:
        Output data with processed image path
    """
    try:
        controller = ImageOverlayController()
        
        def progress_callback(progress):
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress.current,
                    'total': progress.total,
                    'percentage': progress.percentage,
                    'message': progress.message
                }
            )
        
        # Use synchronous version for Celery task
        result = controller.process_overlay_sync(
            input_data,
            progress_callback=progress_callback
        )
        
        return result
    except Exception as e:
        logger.error(f"Error in image overlay task: {str(e)}")
        raise

@celery_app.task(base=BaseTask, bind=True)
def convert_image_to_video(self, job_id: str, input_data: dict) -> dict:
    """
    Convert image to video.
    
    Args:
        job_id: ID of the job
        input_data: Input data containing image path and settings
        
    Returns:
        Output data with video path
    """
    try:
        controller = ImageToVideoController()
        
        def progress_callback(progress):
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress.current,
                    'total': progress.total,
                    'percentage': progress.percentage,
                    'message': progress.message
                }
            )
        
        # Use synchronous version for Celery task
        result = controller.create_video_sync(
            input_data,
            progress_callback=progress_callback
        )
        
        return result
    except Exception as e:
        logger.error(f"Error in image to video task: {str(e)}")
        raise

@celeryd_after_setup.connect
def setup_direct_queue(sender, instance, **kwargs):
    """Setup worker."""
    logger.info("Celery worker initialized")
