"""
Job queue models and types.
"""
from enum import Enum
from typing import Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel

class JobType(str, Enum):
    """Types of jobs that can be processed."""
    MEDIA_DOWNLOAD = "media_download"
    MEDIA_TRANSCRIPTION = "media_transcription"
    IMAGE_OVERLAY = "image_overlay"
    IMAGE_TO_VIDEO = "image_to_video"
    VIDEO_MERGE = "video_merge"
    VIDEO_EXTRACT = "video_extract"
    AUDIO_EXTRACT = "audio_extract"
    AUDIO_MERGE = "audio_merge"

class JobStatus(str, Enum):
    """Possible states of a job."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class JobProgress(BaseModel):
    """Progress information for a job."""
    current: int = 0
    total: int = 100
    percentage: float = 0.0
    message: Optional[str] = None

class Job(BaseModel):
    """Job model representing a background task."""
    id: str
    type: JobType
    status: JobStatus
    user_id: str
    is_premium: bool = False
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[JobProgress] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None

class JobResponse(BaseModel):
    """API response model for job creation/status."""
    job_id: str
    status: JobStatus
    message: Optional[str] = None

class JobStatusResponse(BaseModel):
    """API response model for detailed job status."""
    job_id: str
    status: JobStatus
    progress: Optional[JobProgress] = None
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
