"""
Media download routes for v2 API.
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from typing import Optional, List
from app.models.job import JobResponse, JobStatusResponse
from app.utils.storage import storage_provider
from app.utils.media_downloader import media_downloader, MediaInfo, DownloadFormat
from app.utils.job_queue import job_queue, JobType
from app.utils.auth import get_current_user, User, require_premium

router = APIRouter()

class MediaInfoResponse(BaseModel):
    url: str
    info: MediaInfo

class DownloadRequest(BaseModel):
    url: HttpUrl
    format_id: Optional[str] = None

@router.get("/info", response_model=MediaInfoResponse)
async def get_media_info(url: str, current_user: User = Depends(get_current_user)):
    """Get information about a media URL."""
    try:
        info = await media_downloader.get_media_info(url)
        return MediaInfoResponse(url=url, info=info)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/start", response_model=JobResponse)
@require_premium()
async def start_download(
    request: DownloadRequest,
    current_user: User = Depends(get_current_user)
):
    """Start a media download job."""
    try:
        # Create job
        job = await job_queue.create_job(
            job_type=JobType.MEDIA_DOWNLOAD,
            input_data={
                "url": str(request.url),
                "format_id": request.format_id
            },
            user_id=current_user.username,
            is_premium=current_user.is_premium
        )
        
        return JobResponse(
            job_id=job.id,
            status=job.status,
            message="Download job created successfully"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_download_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get the status of a download job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            progress=job.progress,
            output=job.output_data,
            error=job.error
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/cancel/{job_id}")
async def cancel_download(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Cancel a download job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        success = await job_queue.cancel_job(job_id)
        if success:
            return {"message": "Job cancelled successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to cancel job")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
