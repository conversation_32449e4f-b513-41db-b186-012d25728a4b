"""
Image to video conversion routes.
"""
from typing import Optional, List
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from app.models.job import JobResponse, JobStatusResponse, JobType
from app.utils.auth import require_premium, User, get_current_user
from app.utils.storage import storage_provider
from app.utils.job_queue import job_queue

router = APIRouter()

@router.post("/create", response_model=JobResponse)
@require_premium()
async def create_video(
    image: UploadFile = File(...),
    duration: float = Form(10.0),
    fps: int = Form(30),
    resolution: str = Form("1920x1080"),
    audio: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_user)
):
    """Create a video from an image."""
    image_path = None
    audio_path = None
    
    try:
        # Parse resolution
        try:
            width, height = map(int, resolution.split('x'))
        except:
            raise ValueError("Invalid resolution format. Use WIDTHxHEIGHT (e.g. 1920x1080)")

        # Save uploaded files
        image_path = await storage_provider.save_upload(image, image.filename)
        if audio:
            audio_path = await storage_provider.save_upload(audio, audio.filename)
        
        # Create job
        job = await job_queue.create_job(
            job_type=JobType.IMAGE_TO_VIDEO,
            input_data={
                "image_path": image_path,
                "duration": duration,
                "fps": fps,
                "resolution": (width, height),
                "audio_path": audio_path
            },
            user_id=current_user.username,
            is_premium=current_user.is_premium
        )
        
        return JobResponse(
            job_id=job.id,
            status=job.status,
            message="Video creation job started"
        )
    except Exception as e:
        # Cleanup uploaded files if job creation fails
        if image_path:
            await storage_provider.delete_file(image_path)
        if audio_path:
            await storage_provider.delete_file(audio_path)
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_video_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get status of video creation job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            progress=job.progress,
            output=job.output_data,
            error=job.error
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/cancel/{job_id}")
async def cancel_video_creation(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Cancel a video creation job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        success = await job_queue.cancel_job(job_id)
        if success:
            # Cleanup any temporary files
            if job.input_data:
                if 'image_path' in job.input_data:
                    await storage_provider.delete_file(job.input_data['image_path'])
                if 'audio_path' in job.input_data:
                    await storage_provider.delete_file(job.input_data['audio_path'])
            return {"message": "Job cancelled successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to cancel job")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
