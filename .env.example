# Application
DEBUG=true
APP_NAME="Media Processing API"
VERSION="2.0.0"
API_PREFIX="/api/v1"

# Security
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Storage
STORAGE_TYPE=local  # local, s3, or gcp
TEMP_DIR=/tmp/media_processing

# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_BUCKET=your-bucket-name

# Google Cloud Storage
GCP_PROJECT_ID=your-project-id
GCP_CREDENTIALS_PATH=/path/to/credentials.json
GCS_BUCKET=your-bucket-name

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Media Processing
MAX_UPLOAD_SIZE=104857600  # 100MB
SUPPORTED_VIDEO_FORMATS=mp4,mov,avi,mkv
SUPPORTED_AUDIO_FORMATS=mp3,wav,aac,flac
SUPPORTED_IMAGE_FORMATS=jpg,png,gif,webp

# Whisper Configuration
WHISPER_MODEL=base  # Options: base, small, medium, large

# Premium Features
PREMIUM_VIDEO_EFFECTS=["blur", "grayscale", "sepia"]
PREMIUM_AUDIO_EFFECTS=["echo", "reverb"]
