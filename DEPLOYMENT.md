# DahoPevi API Deployment Guide

## Overview

This application supports both v1 (Flask) and v2 (FastAPI) endpoints and is ready for deployment on Coolify or any Docker-based platform.

## Architecture

- **v2 API (FastAPI)**: Modern async API with enhanced features (default)
- **v1 API (Flask)**: Legacy endpoints for backward compatibility
- **Unified Deployment**: Single Docker container can run both versions

## Environment Variables

### Required
```bash
# API Configuration
API_VERSION=v2                    # Options: v1, v2, both
STORAGE_TYPE=local               # Options: local, s3, gcp
REDIS_HOST=redis                 # Redis connection

# Security
JWT_SECRET_KEY=your-secret-key
API_KEY=your-api-key
```

### Optional Storage (S3)
```bash
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_BUCKET=your-bucket-name
```

### Optional Storage (GCP)
```bash
GCP_PROJECT_ID=your-project-id
GCP_CREDENTIALS_PATH=/path/to/credentials.json
GCS_BUCKET=your-bucket-name
```

### Optional Media Processing
```bash
WHISPER_MODEL=base               # Options: base, small, medium, large
```

## Deployment Options

### Option 1: v2 Only (Recommended)
```bash
API_VERSION=v2
```
- Runs FastAPI app on port 8000
- Modern async endpoints
- Better performance and documentation

### Option 2: v1 Only (Legacy)
```bash
API_VERSION=v1
```
- Runs Flask app on port 8000
- All legacy endpoints available
- For backward compatibility

### Option 3: Both APIs
```bash
API_VERSION=both
```
- v1 (Flask) on port 8001
- v2 (FastAPI) on port 8000
- Full backward compatibility

## Coolify Deployment

1. **Create New Project** in Coolify
2. **Set Repository**: Point to your Git repository
3. **Configure Environment Variables**:
   ```
   API_VERSION=v2
   STORAGE_TYPE=local
   REDIS_HOST=redis
   JWT_SECRET_KEY=your-secret-key
   ```
4. **Set Build Context**: Root directory
5. **Set Port**: 8000
6. **Deploy**

## Health Checks

- **v2 Health**: `GET /api/v2/health`
- **v1 Health**: `GET /v1/toolkit/test`
- **Root Info**: `GET /`

## API Documentation

- **v2 Docs**: `/api/v2/docs` (Swagger UI)
- **v2 ReDoc**: `/api/v2/redoc`
- **v1 Info**: Available at root endpoint

## Features by Version

### v2 (FastAPI) Features
- ✅ Async processing
- ✅ Automatic API documentation
- ✅ Type validation with Pydantic
- ✅ Premium user features
- ✅ Enhanced error handling
- ✅ Background job processing

### v1 (Flask) Features
- ✅ All legacy endpoints
- ✅ Backward compatibility
- ✅ Existing integrations
- ✅ File serving capabilities

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are in requirements.txt
2. **Storage Issues**: Check STORAGE_TYPE and related credentials
3. **Redis Connection**: Verify REDIS_HOST is accessible
4. **Port Conflicts**: Ensure ports 8000/8001 are available

### Logs
```bash
docker logs <container-name>
```

### Health Check
```bash
curl http://localhost:8000/api/v2/health
```

## Production Considerations

1. **Use External Redis**: Don't rely on container Redis for production
2. **Configure Storage**: Use S3 or GCS for file storage
3. **Set Secrets**: Use proper secret management for API keys
4. **Monitor**: Set up logging and monitoring
5. **Scale**: Use multiple workers for high load

## Migration from v1 to v2

1. **Test v2 endpoints** with your existing workflows
2. **Update client applications** to use v2 endpoints
3. **Switch API_VERSION** from v1 to v2
4. **Monitor** for any issues
5. **Remove v1** when no longer needed
